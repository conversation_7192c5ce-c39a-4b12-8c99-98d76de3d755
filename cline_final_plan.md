# Итоговый отчёт и план исправлений для Armwrestling Power Arena

## 1. Краткое резюме
Проект основан на Next.js 15 (App Router), React 19, Supabase, Tailwind CSS и Feature-Sliced Design. Архитектура в целом корректна, но выявлены критические проблемы в безопасности, производительности, тестировании и качестве кода:

- **Безопасность**: SSRF, Stored XSS, отсутствие CSP, небезопасная загрузка файлов, доверие клиентским параметрам (`pointsAwarded`), функции с `SECURITY DEFINER`.
- **Производительность**: лишние запросы к БД в middleware, большие bundle, неиспользованный кеш, отсутствие оптимизации изображений.
- **Качество кода**: дублирование кода (Supabase-клиенты, credentials), «бог-компоненты» (Navbar, AuthProvider), усложнённая утилита `cn`, конфликт линтеров (Biome vs ESLint).
- **Тестирование**: минимальное покрытие Vitest, нет тестов для middleware, Server Actions, RLS-политик.
- **Документация**: рассинхронизация `docs/todo.md` и комментариев `// TODO`, устаревшие ревью-отчёты, неполные гайды.

## 2. Приоритетные задачи и фиксы

### 🔴 Критично

1. **Устранить SSRF-уязвимость в видеозагрузке**
   *Файл:* `src/features/submissions/actions.ts`
   *Решение:* запретить любые URL, кроме разрешённого списка.
   ```typescript
   const ALLOWED_HOSTS = ['youtube.com','youtu.be','tiktok.com','instagram.com'];
   function isValidVideoUrl(url: string): boolean {
     try {
       const { hostname } = new URL(url);
       return ALLOWED_HOSTS.some(h => hostname === h || hostname.endsWith(`.${h}`));
     } catch {
       return false;
     }
   }
   if (!isValidVideoUrl(videoUrl)) {
     return { error: 'Недопустимый формат videoUrl' };
   }
   // Удалить fetch(videoUrl) HEAD-запрос
   ```
2. **Добавить Content Security Policy**
   *Файл:* `next.config.ts`
   ```js
   module.exports = {
     async headers() {
       return [{
         source: '/:path*',
         headers: [
           {
             key: 'Content-Security-Policy',
             value:
               "default-src 'self'; script-src 'self'; style-src 'self'; " +
               "img-src 'self' data: https:; connect-src 'self' https://*.supabase.co;"
           }
         ]
       }];
     }
   }
   ```
3. **Санитизировать вывод пользовательского HTML**
   *Файл:* `src/app/(app)/exercises/[id]/page.tsx`
   ```tsx
   import DOMPurify from 'dompurify';
   // …
   <div
     dangerouslySetInnerHTML={{
       __html: DOMPurify.sanitize(exercise.description || '')
     }}
   />
   ```
4. **Ограничить загрузку файлов по размеру и MIME-типу**
   *Файл (Server Action):* `src/features/profile/actions.ts`
   ```typescript
   const file = formData.get('avatar') as File;
   if (file.size > 2 * 1024 * 1024) {
     return { error: 'Максимальный размер файла — 2 МБ' };
   }
   const allowed = ['image/png','image/jpeg'];
   if (!allowed.includes(file.type)) {
     return { error: 'Недопустимый формат файла' };
   }
   // загрузка в Supabase Storage
   ```
5. **Переместить роль пользователя в JWT claims**
   *Миграция SQL (supabase/migrations/...):*
   ```sql
   create or replace function public.get_claims(uid uuid, claims jsonb) returns jsonb
   as $$
   begin
     return jsonb_set(claims, '{app_metadata,role}',
       to_jsonb((select role from public.profiles where id = uid))
     );
   end;
   $$ language plpgsql stable security definer;
   ```
   *Middleware (`src/middleware.ts`):*
   ```ts
   const user = response.data.user;
   const role = user?.app_metadata?.role as UserRole | undefined;
   // больше не делаем запрос к profiles
   ```

### 🟡 Высокий

6. **Фикс проверки начисления очков на сервере**
   *Файл:* `src/features/submissions/evaluationActions.ts`
   ```ts
   // Вместо доверия pointsAwarded из клиента:
   const points = MEDAL_POINTS[validatedData.medalAwarded];
   await supabase.rpc('increment_user_points', { user_id_param, points_param: points });
   ```
7. **Перефакторить создание Supabase-клиентов**
   *Файл:* `src/libs/supabase/server.ts`
   ```ts
   // Объединить повторяющийся код в одну фабрику:
   function createActionClient(cookiesStore?) {
     const { SUPABASE_URL, SUPABASE_ANON, SUPABASE_SERVICE_ROLE } = getCredentials();
     return createClient(
       SUPABASE_URL,
       SUPABASE_SERVICE_ROLE,
       { cookies: cookiesStore }
     );
   }
   export const createServerActionClient = createActionClient;
   ```
8. **Убрать дублирование в credentials.ts**
   ```ts
   function resolveEnv() {
     const isLocal = process.env.NODE_ENV !== 'production';
     const url = process.env[
       isLocal ? 'NEXT_PUBLIC_SUPABASE_URL' : 'NEXT_PUBLIC_SUPABASE_URL_PROD'
     ]!;
     const key = process.env[
       isLocal ? 'NEXT_PUBLIC_SUPABASE_ANON_KEY' : 'NEXT_PUBLIC_SUPABASE_ANON_KEY_PROD'
     ]!;
     return { url, key };
   }
   ```
9. **Унифицировать линтинг: перейти на Biome**
   - Удалить `eslint.config.mjs` и зависимости ESLint
   - Обновить `package.json` → `"lint": "biome check"`
   - Настроить pre-commit hook (Husky) для Biome
10. **Добавить rate limiting middleware**
    ```ts
    import { Ratelimit } from '@upstash/ratelimit';
    const ratelimit = new Ratelimit({ redis, limiter: Ratelimit.fixedWindow(100, '1m') });
    export default async function middleware(req: NextRequest) {
      const { success } = await ratelimit.limit(req.ip);
      if (!success) return new NextResponse('Too Many Requests', { status: 429 });
      // далее ваша логика
    }
    ```

### 🟢 Средний и низкий

11. **Разбить «бог-компоненты» на мелкие**
    - Navbar → `useNavbarVisibility()`, `NavLinks`
    - AuthProvider → `AuthContext`, `SessionProvider`
    - SubmissionCard → `useVideoUpload`, `VideoPlayer`
12. **Оптимизировать запрос рейтингов**
    - Создать RPC `get_ranked_profiles` с JOIN и агрегацией
    - Заменить двойной запрос на единый
13. **Динамический импорт тяжёлых компонентов**
    ```ts
    const SubmissionForm = dynamic(
      () => import('@/features/submissions/SubmissionForm'),
      { ssr: false }
    );
    ```
14. **Перенести утилиту `cn` в простую версию**
    ```ts
    import { clsx } from 'clsx';
    import { twMerge } from 'tailwind-merge';
    export function cn(...args: ClassValue[]) {
      return twMerge(clsx(args));
    }
    ```
15. **Использовать `next/image` для оптимизации изображений**
16. **Добавить Bundle Analyzer**
    ```js
    // next.config.ts
    const withBundleAnalyzer = require('@next/bundle-analyzer')({ enabled: process.env.ANALYZE === 'true' });
    module.exports = withBundleAnalyzer({ /* existing config */ });
    ```
17. **Покрыть тестами ключевые участки**
    - Middleware (авторизация, public маршруты)
    - Server Actions (auth, submissions, profile)
    - RPC-функции и RLS-политики (интеграционные тесты)
18. **Синхронизировать TODOs**
    - Перенести все `// TODO` в `docs/todo.md`
    - Настроить CI-правило: ошибаться при наличии `TODO` в продакшен-коде
19. **Включить строгий режим TypeScript**
    ```json
    "compilerOptions": {
      "strict": true,
      "noUncheckedIndexedAccess": true,
      "exactOptionalPropertyTypes": true
    }
    ```
20. **Добавить JSDoc/TSDoc и обновить гайды**
    - Описать API Server Actions, хуки, утилиты
    - Обновить устаревшую часть в `docs/`

---

Выполнение этого плана приведёт проект к уровню enterprise: безопасность, производительность, тестируемость и читаемость кода будут на высоком уровне.
