# Arm Wrestling Power Arena – Remediation Plan (2025-07-01)

This file translates the consolidated audit into an actionable, time-boxed plan for the engineering team.  Each task includes an owner placeholder (`@team-handle`) – please replace with real names once sprint capacity is allocated.

## Guiding Principles

1. **Security first** – P0 items are non-negotiable and will block release.
2. **Keep master green** – every change must ship with passing lint/tests.
3. **One source of truth** – update this document and `docs/todo.md` when scope changes.

---

## Timeline Overview

| Sprint | Calendar Dates | Goal | Success Criteria |
|--------|----------------|------|------------------|
| Sprint 0 (hot-fix) | ≤ 1 week | Eliminate Critical (P0) risks | No failing security tests; RLS fully enabled; Supabase client misuse removed |
| Sprint 1 | week 2-3 | Close High (P1) issues & architectural debt | CI includes lint+tests; bundle size reduced; email confirmation gated |
| Sprint 2 | week 4-5 | Address Medium/Low items, harden processes | 60 % test coverage; docs & tooling up-to-date |

---

## Detailed Task Breakdown

### Sprint 0 – Critical Hot-fixes (blocker)

| ID | Task | Owner | Est. | Notes |
|----|------|-------|------|-------|
| S-1 | Standardise & enable RLS on all tables | @backend | 1 d | Central SQL migration + helper funcs in `security` schema |
| S-2 | Embed `role` into JWT claims via `custom_access_token` hook | @backend | 0.5 d | Includes middleware refactor |
| S-3 | Replace unsafe Supabase client usage (`serverPermissions`, `protectedApi`, `app/exercises/*`) | @fullstack | 1 d | Introduce `createServerActionClient` util + ESLint rule |
| S-4 | Harden `increment_user_points` RPC (remove `SECURITY DEFINER` or add checks) | @backend | 0.5 d | Add unit test |
| T-1 | Establish minimum critical test suite (middleware, RLS) | @qa | 1 d | Target ≥20 % lines |

**Exit criteria:** all P0 tests green; no unauthorised DB access paths remain.

---

### Sprint 1 – High-Priority & Architecture

| ID | Task | Owner | Est. | Dependencies |
|----|------|-------|------|--------------|
| S-5 | Add URL allow-list for video ingestion (SSRF) | @backend | 0.5 d | – |
| S-6 | Global security headers via Next `headers()` | @frontend | 0.5 d | – |
| S-7 | Remove `dangerouslySetInnerHTML` from PWA page | @frontend | 0.5 d | – |
| S-8 | Purge `.env.local` from repo & rotate keys | @devops | 0.2 d | – |
| S-9 | Enable email confirmation in prod config; add CI check | @devops | 0.3 d | – |
| P-1 | Replace N+1 ranking query with SQL view | @backend | 0.5 d | – |
| A-1 | Split mixed client/server auth util | @frontend | 0.5 d | S-3 |
| A-4 | Move stray SQL functions into migrations | @backend | 0.3 d | – |
| T-2 | Add GitHub Action: `biome check` + `vitest` | @devops | 0.5 d | depends on T-1 |

**Exit criteria:** CI blocks merges on lint & tests; high-severity vulnerabilities resolved.

---

### Sprint 2 – Medium/Low & Hardening

| ID | Task | Owner | Est. | Notes |
|----|------|-------|------|-------|
| P-3 | Bundle optimisation (tree-shake, dynamic import) | @frontend | 1 d | Measure via `next-bundle-analyzer` |
| P-4 | Add `revalidatePath` / `revalidateTag` to list pages | @frontend | 0.5 d | – |
| A-2 | Centralise credential resolution util | @frontend | 0.5 d | – |
| A-3 | Refactor `AuthProvider` into 3 contexts | @frontend | 1 d | – |
| C-1 | Migrate inline TODOs to docs; delete comments | @all | 0.5 d | Use Biome rule to enforce |
| C-2 | Remove ESLint; adopt Biome only | @frontend | 0.5 d | Update pre-commit |
| C-3 | Remove outdated Supabase test scripts | @backend | 0.2 d | – |
| C-4 | Format SQL to lowercase | @backend | 0.3 d | Scriptable |
| C-5 | Cleanup outdated examples | @frontend | 0.2 d | – |
| T-3 | Add migration-reset test job | @devops | 0.5 d | – |
| T-4 | Write `docs/deploy.md` & rollback guide | @tech-writer | 0.5 d | – |

**Exit criteria:** ≥60 % coverage; docs and CI fully reflect new standards.

---

## Risks & Mitigations

* **Database migration conflicts** – run branch-strict migration policy; dry-run in staging.
* **JWT claim rollout** – existing sessions become invalid → announce maintenance window.
* **CI flakiness** – use fixed Supabase test project; seed before tests.

---

## Change-management Checklist

- [ ] Update `CHANGELOG.md` per sprint
- [ ] Version bump following SemVer (patch for Sprint 0, minor for Sprint 1, 2)
- [ ] Security review sign-off before releasing to prod

---

_End of file_
