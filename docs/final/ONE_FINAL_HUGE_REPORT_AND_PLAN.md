# Comprehensive Report and Fix Plan: Armwrestling Power Arena

## Executive Summary

The "Armwrestling Power Arena" project demonstrates a modern and robust architectural foundation powered by technologies like Next.js 15, React 19, and Supabase. Despite this, the project faces several critical issues that range from security vulnerabilities to performance bottlenecks and code maintainability challenges. This comprehensive report details each identified issue and provides a thorough plan with code examples to address these challenges, thereby elevating the codebase to a state of excellence.

## Detailed Analysis and Proposed Solutions

---

### 1. Security Vulnerabilities

#### 1.1 Server-Side Request Forgery (SSRF) Risk

- **Location:** `src/features/submissions/actions.ts`
- **Description:** Unfiltered URL inputs can lead to SSRF, enabling attackers to make unauthorized requests within the internal network.
- **Proposed Solution: Code Implementation**

```javascript
const ALLOWED_VIDEO_DOMAINS = [
  'youtube.com', 'youtu.be', 'tiktok.com', 'instagram.com'
];

function isValidVideoUrl(url) {
  try {
    const urlObj = new URL(url);
    return ALLOWED_VIDEO_DOMAINS.includes(urlObj.hostname.toLowerCase());
  } catch {
    return false;
  }
}

app.post('/submit-video', (req, res) => {
  const { videoUrl } = req.body;
  if (!isValidVideoUrl(videoUrl)) {
    return res.status(400).send('Invalid URL');
  }
  // Proceed with safe URL handling...
});
```

#### 1.2 Role-Based Access Control (RBAC) Inconsistency

- **Locations:** `middleware.ts`, `supabase/migrations/`
- **Description:** Inconsistent role checks between queries and token metadata lead to authorization lapses.
- **Proposed Solution: Refactoring**

```typescript
// Migration to add role claims to JWT
db.query(`
CREATE OR REPLACE FUNCTION get_claims(uid uuid, claims jsonb)
RETURNS jsonb
LANGUAGE sql STABLE
AS $$
  SELECT jsonb_set(claims,
    '{user_metadata,role}', to_jsonb((SELECT role FROM public.profiles WHERE id = uid)));
$$;

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
GRANT SELECT ON TABLE profiles TO anon;
`);

// Update middleware to use JWT claims
const { user } = await supabase.auth.api.getUser(session.access_token);
const role = user?.user_metadata?.role;
if (role !== 'admin') {
  return res.status(403).send('Access denied');
}
```

---

### 2. Performance Bottlenecks

#### 2.1 Database Query Overhead

- **Location:** `src/middleware.ts`
- **Description:** Frequent role-querying from the database hampers performance.
- **Proposed Solution: Optimizing Role Checks**

```typescript
// Enhance Supabase JWT with user roles and access them from middleware
middleware.ts:
...
app.use(async (req, res, next) => {
  const user = await supabase.auth.api.getUser(session.access_token);
  if (!user) return res.status(401).send('Unauthorized');
  const role = user?.user_metadata?.role;
  req.userRole = role;
  next();
});
...
```

---

### 3. Code Quality and Maintainability

#### 3.1 Code Duplication

- **Locations:** `src/lib/supabase/server.ts`, `src/lib/supabase/credentials.ts`
- **Description:** Repeated logic across files leads to redundancy and maintenance complexity.
- **Proposed Solution: Consolidation**

```typescript
// Refactor code to consolidate duplicated logic into reusable functions
function createSupabaseClient(env) {
  const supabaseUrl = process.env[`NEXT_PUBLIC_SUPABASE_URL_${env}`];
  const supabaseAnonKey = process.env[`NEXT_PUBLIC_SUPABASE_ANON_KEY_${env}`];
  return createClient(supabaseUrl, supabaseAnonKey);
}
const supabaseLocal = createSupabaseClient('LOCAL');
const supabaseProd = createSupabaseClient('PROD');
```

---

### 4. Linting and Formatting Conflicts

#### 4.1 Biome vs. ESLint

- **Location:** `biome.json`, `eslint.config.mjs`
- **Description:** Conflict between linters leads to unclear code style guidelines.
- **Proposed Solution: Single Source of Truth**

```json
// Choose Biome for comprehensive linting;
// remove other conflicting configurations
"formatter": { "enabled": true, "lineWidth": 80 },
"linter": { "enabled": true, "rules": { "recommended": true } },
```

---

### Conclusion and Next Steps

By implementing the proposed solutions outlined above, "Armwrestling Power Arena" can achieve significant improvements in security, performance, and maintainability. The focus should be on adopting a more consolidated approach to handling tokens and roles, reducing database calls, enhancing security checks, and maintaining consistency across the entire codebase.

Completing these steps will ensure the project becomes a benchmark for best practices in scalable and resilient application development.
